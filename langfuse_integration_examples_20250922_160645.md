# Langfuse 第三方系统集成示例

## 基础配置

- **Langfuse URL**: http://localhost:3000
- **API Key**: 未配置
- **Project ID**: 未配置

## JavaScript/TypeScript 集成示例

### 使用 API 密钥认证

```javascript
const LANGFUSE_CONFIG = {
  baseUrl: 'http://localhost:3000',
  apiKey: 'your-api-key',
  timeout: 10000
};

// 测试连接
async function testConnection() {
  try {
    const response = await fetch(`${LANGFUSE_CONFIG.baseUrl}/api/public/health`);
    const data = await response.json();
    console.log('Langfuse 连接成功:', data);
  } catch (error) {
    console.error('连接失败:', error);
  }
}

// 获取提示词列表
async function getPrompts() {
  try {
    const response = await fetch(`${LANGFUSE_CONFIG.baseUrl}/api/public/v2/prompts?limit=10`, {
      headers: {
        'Authorization': `Bearer ${LANGFUSE_CONFIG.apiKey}`
      }
    });
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('获取提示词失败:', error);
  }
}
```

### 使用项目 ID（无认证模式）

```javascript
const LANGFUSE_CONFIG = {
  baseUrl: 'http://localhost:3000',
  projectId: 'your-project-id',
  timeout: 10000
};

// 搜索提示词
async function searchPrompts(query) {
  try {
    const params = new URLSearchParams({
      projectId: LANGFUSE_CONFIG.projectId,
      search: query,
      limit: 10
    });
    
    const response = await fetch(`${LANGFUSE_CONFIG.baseUrl}/api/public/v2/prompts?${params}`);
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('搜索提示词失败:', error);
  }
}
```

## Python 集成示例

```python
import requests
import json

class LangfuseClient:
    def __init__(self, base_url='http://localhost:3000', api_key='your-api-key'):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def test_connection(self):
        try:
            response = requests.get(f'{self.base_url}/api/public/health', timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f'连接测试失败: {e}')
            return None
    
    def get_prompts(self, limit=10):
        try:
            response = requests.get(
                f'{self.base_url}/api/public/v2/prompts',
                headers=self.headers,
                params={'limit': limit},
                timeout=10
            )
            response.raise_for_status()
            return response.json()['data']
        except Exception as e:
            print(f'获取提示词失败: {e}')
            return None

# 使用示例
client = LangfuseClient()
health = client.test_connection()
if health:
    print(f"连接成功，版本: {health.get('version')}")
    prompts = client.get_prompts()
    print(f"找到 {len(prompts)} 个提示词")
```

## cURL 测试命令

```bash
# 健康检查
curl -X GET "http://localhost:3000/api/public/health"

# 获取提示词列表（认证模式）
curl -X GET "http://localhost:3000/api/public/v2/prompts?limit=5" \
  -H "Authorization: Bearer your-api-key"

# 搜索提示词（无认证模式）
curl -X GET "http://localhost:3000/api/public/v2/prompts?projectId=your-project-id&search=test&limit=5"
```

## 常见错误处理

1. **Connection refused**: 检查 Langfuse 服务是否运行
2. **401 Unauthorized**: 检查 API 密钥是否正确
3. **404 Not Found**: 检查 API 端点路径是否正确
4. **Timeout**: 增加超时时间或检查网络连接

