# 第三方系统集成连接错误排查指南

## 问题描述

在配置第三方系统（如 RAGFlow、Dify 等）与 Langfuse 集成时，可能会遇到以下连接错误：

- `ConnectError('[<PERSON>rrno 111] Connection refused')`
- `ECONNREFUSED`
- `ENOTFOUND`
- `ECONNRESET`
- 连接超时错误

## 🔍 错误诊断步骤

### 1. 检查 Langfuse 服务状态

```bash
# 检查服务是否运行
curl -v http://localhost:3000/api/public/health

# 检查服务就绪状态
curl -v http://localhost:3000/api/public/ready

# 检查端口监听状态
netstat -an | grep LISTEN | grep -E "(3000|6379|5432|8123|9000|9090)"

# 检查 Docker 容器状态
docker ps -a
```

### 2. 验证网络连接

```bash
# 检查基本网络连通性
ping localhost
ping 127.0.0.1

# 检查端口连通性
telnet localhost 3000

# 检查防火墙设置
sudo ufw status  # Ubuntu/Debian
sudo firewall-cmd --list-all  # CentOS/RHEL
```

### 3. 检查配置文件

检查 `.env` 文件中的配置：

```bash
# 检查关键配置项
grep -E "(NEXTAUTH_URL|DATABASE_URL|REDIS_HOST|CLICKHOUSE_URL)" .env
```

## 🛠️ 常见解决方案

### 解决方案 1: 检查服务地址配置

**问题**: 第三方系统配置的 Langfuse 地址不正确

**解决方法**:
```bash
# 确认 Langfuse 实际运行地址
curl http://localhost:3000/api/public/health

# 如果使用 Docker，检查容器网络
docker network ls
docker inspect langfuse-web-1
```

**正确配置示例**:
- 本地开发: `http://localhost:3000`
- Docker 内部: `http://langfuse-web:3000`
- 生产环境: `https://your-domain.com`

### 解决方案 2: 修复端口冲突

**问题**: 端口被其他服务占用

**解决方法**:
```bash
# 查找占用端口的进程
lsof -i :3000
sudo netstat -tlnp | grep :3000

# 停止冲突的服务或修改端口配置
# 在 .env 中修改端口（如果支持）
```

### 解决方案 3: 重启相关服务

**问题**: 服务状态异常

**解决方法**:
```bash
# 重启 Langfuse 服务
pnpm run dev:web

# 或重启 Docker 服务
docker-compose restart langfuse-web

# 重启依赖服务
docker-compose restart postgres redis clickhouse minio
```

### 解决方案 4: 检查防火墙和网络策略

**问题**: 防火墙阻止连接

**解决方法**:
```bash
# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw reload

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload

# 检查 iptables 规则
sudo iptables -L -n
```

### 解决方案 5: 验证 API 端点

**问题**: 使用了错误的 API 端点

**正确的端点格式**:
```
# 健康检查
GET /api/public/health

# 就绪检查  
GET /api/public/ready

# 提示词推荐 API
GET /api/public/v2/prompts/recommend

# 提示词搜索 API
GET /api/public/v2/prompts

# 跟踪数据接口
POST /api/public/ingestion
```

## 🔧 第三方系统特定配置

### RAGFlow 集成配置

```python
# RAGFlow 中的 Langfuse 配置
LANGFUSE_BASE_URL = "http://localhost:3000"  # 本地开发
# LANGFUSE_BASE_URL = "https://your-langfuse-domain.com"  # 生产环境

# 测试连接
import requests
response = requests.get(f"{LANGFUSE_BASE_URL}/api/public/health")
print(f"Langfuse 状态: {response.json()}")
```

### Dify 集成配置

```yaml
# Dify 配置文件
langfuse:
  base_url: "http://localhost:3000"
  api_key: "your-api-key"
  project_id: "your-project-id"
```

### 自定义集成配置

```javascript
// JavaScript/Node.js 集成示例
const LANGFUSE_CONFIG = {
  baseUrl: process.env.LANGFUSE_BASE_URL || 'http://localhost:3000',
  apiKey: process.env.LANGFUSE_API_KEY,
  timeout: 10000, // 10秒超时
  retries: 3
};

// 连接测试函数
async function testLangfuseConnection() {
  try {
    const response = await fetch(`${LANGFUSE_CONFIG.baseUrl}/api/public/health`, {
      timeout: LANGFUSE_CONFIG.timeout
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Langfuse 连接成功:', data);
      return true;
    } else {
      console.error('Langfuse 连接失败:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Langfuse 连接错误:', error.message);
    return false;
  }
}
```

## 📋 完整诊断检查清单

### 基础检查
- [ ] Langfuse 服务正在运行
- [ ] 端口 3000 可访问
- [ ] 健康检查端点返回 200 状态
- [ ] 数据库连接正常
- [ ] Redis 连接正常

### 网络检查
- [ ] 防火墙允许相关端口
- [ ] DNS 解析正常（如使用域名）
- [ ] 网络路由可达
- [ ] 代理设置正确（如有）

### 配置检查
- [ ] API 地址配置正确
- [ ] API 密钥有效（如需要）
- [ ] 项目 ID 正确（如使用）
- [ ] 超时设置合理

### 第三方系统检查
- [ ] 第三方系统网络配置正确
- [ ] 第三方系统有访问权限
- [ ] 第三方系统版本兼容
- [ ] 集成代码实现正确

## 🆘 获取帮助

如果以上解决方案都无法解决问题，请：

1. **收集诊断信息**:
   ```bash
   # 收集系统信息
   curl -v http://localhost:3000/api/public/health > health_check.log 2>&1
   docker ps -a > docker_status.log
   netstat -an | grep LISTEN > ports.log
   ```

2. **查看日志**:
   ```bash
   # Langfuse 应用日志
   docker logs langfuse-web-1
   
   # 系统日志
   journalctl -u langfuse --since "1 hour ago"
   ```

3. **联系支持**:
   - GitHub Issues: https://github.com/langfuse/langfuse/issues
   - Discord 社区: https://discord.gg/7NXusRtqYU
   - 官方文档: https://langfuse.com/docs

## 📚 相关文档

- [Langfuse API 文档](https://langfuse.com/docs/api)
- [第三方集成指南](../third-party-integration/)
- [部署指南](../deployment/)
- [网络配置指南](../networking/)
