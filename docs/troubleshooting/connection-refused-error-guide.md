# ConnectError('[<PERSON><PERSON><PERSON> 111] Connection refused') 错误解决指南

## 🚨 问题描述

当第三方系统（如 RAGFlow、Dify、自定义应用等）尝试连接 Langfuse 时，可能会遇到以下错误：

```
ConnectError('[<PERSON>rrno 111] Connection refused')
```

这个错误表示客户端无法连接到 Langfuse 服务器，通常是由于网络连接、服务配置或防火墙问题导致的。

## 🔍 快速诊断

### 1. 使用自动诊断工具

我们提供了两个诊断脚本来帮助快速定位问题：

```bash
# 全面系统诊断
./scripts/diagnose-connection.sh

# 专门的第三方集成测试
./scripts/test-third-party-integration.sh
```

### 2. 手动检查步骤

#### 步骤 1: 验证 Langfuse 服务状态

```bash
# 检查服务是否运行
curl http://localhost:3000/api/public/health

# 预期响应
{"status":"OK","version":"3.103.0"}
```

#### 步骤 2: 检查端口监听

```bash
# macOS
lsof -i :3000

# Linux
netstat -tlnp | grep :3000

# 或使用通用方法
curl -v http://localhost:3000
```

#### 步骤 3: 检查进程状态

```bash
# 查找 Langfuse 相关进程
ps aux | grep -E "(next|langfuse|node)" | grep -v grep

# 查看 Docker 容器状态（如果使用 Docker）
docker ps | grep langfuse
```

## 🛠️ 解决方案

### 解决方案 1: 启动 Langfuse 服务

**问题**: Langfuse 服务未运行

**解决方法**:

```bash
# 开发模式启动
cd /path/to/langfuse
pnpm run dev:web

# 或使用 Docker
docker-compose up -d langfuse-web

# 验证启动成功
curl http://localhost:3000/api/public/health
```

### 解决方案 2: 检查配置地址

**问题**: 第三方系统配置的 Langfuse 地址不正确

**常见错误配置**:
- ❌ `http://langfuse:3000` (Docker 内部地址，外部无法访问)
- ❌ `https://localhost:3000` (使用了 HTTPS 但服务运行在 HTTP)
- ❌ `http://127.0.0.1:3001` (端口错误)

**正确配置**:
- ✅ `http://localhost:3000` (本地开发)
- ✅ `http://*************:3000` (局域网访问)
- ✅ `https://your-domain.com` (生产环境)

### 解决方案 3: 修复网络连接问题

**问题**: 网络连接被阻止

**解决方法**:

```bash
# 检查防火墙设置
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --list-ports
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload

# 检查网络连通性
ping localhost
telnet localhost 3000
```

### 解决方案 4: Docker 网络配置

**问题**: Docker 容器网络配置问题

**解决方法**:

```bash
# 检查 Docker 网络
docker network ls
docker network inspect langfuse_default

# 重启 Docker 服务
docker-compose restart

# 检查端口映射
docker port langfuse-web-1
```

### 解决方案 5: 环境变量配置

**问题**: 环境变量配置错误

**检查配置**:

```bash
# 检查 .env 文件
cat .env | grep -E "(NEXTAUTH_URL|DATABASE_URL|REDIS_HOST)"

# 确保关键配置正确
NEXTAUTH_URL=http://localhost:3000  # 或你的实际域名
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres
REDIS_HOST=localhost
```

## 🔧 第三方系统特定解决方案

### RAGFlow 集成

```python
# RAGFlow 配置示例
LANGFUSE_CONFIG = {
    "base_url": "http://localhost:3000",  # 确保地址正确
    "timeout": 30,  # 增加超时时间
    "retry_count": 3  # 添加重试机制
}

# 连接测试代码
import requests
try:
    response = requests.get(f"{LANGFUSE_CONFIG['base_url']}/api/public/health", 
                          timeout=LANGFUSE_CONFIG['timeout'])
    if response.status_code == 200:
        print("Langfuse 连接成功")
    else:
        print(f"连接失败，状态码: {response.status_code}")
except requests.exceptions.ConnectionError:
    print("连接被拒绝，请检查 Langfuse 服务是否运行")
except requests.exceptions.Timeout:
    print("连接超时，请检查网络或增加超时时间")
```

### Dify 集成

```yaml
# Dify 配置文件
langfuse:
  enabled: true
  base_url: "http://localhost:3000"  # 确保地址正确
  api_key: "your-api-key"
  timeout: 30
  retry_attempts: 3
```

### 自定义应用集成

```javascript
// JavaScript/Node.js 集成示例
const axios = require('axios');

const langfuseClient = axios.create({
  baseURL: 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加重试逻辑
langfuseClient.interceptors.response.use(
  response => response,
  async error => {
    if (error.code === 'ECONNREFUSED') {
      console.error('Langfuse 连接被拒绝，请检查服务状态');
      // 可以添加重试逻辑或降级处理
    }
    return Promise.reject(error);
  }
);

// 测试连接
async function testConnection() {
  try {
    const response = await langfuseClient.get('/api/public/health');
    console.log('连接成功:', response.data);
  } catch (error) {
    console.error('连接失败:', error.message);
  }
}
```

## 📋 故障排除检查清单

### 基础检查
- [ ] Langfuse 服务正在运行
- [ ] 端口 3000 可访问
- [ ] 健康检查端点返回 200 状态
- [ ] 配置的 URL 地址正确

### 网络检查
- [ ] 防火墙允许端口 3000
- [ ] 网络连通性正常
- [ ] DNS 解析正确（如使用域名）
- [ ] 代理设置正确（如有）

### 配置检查
- [ ] 环境变量配置正确
- [ ] Docker 网络配置正常
- [ ] API 密钥有效（如需要）
- [ ] 项目 ID 正确（如使用）

### 第三方系统检查
- [ ] 第三方系统网络配置正确
- [ ] 超时设置合理
- [ ] 重试机制配置
- [ ] 错误处理逻辑完善

## 🆘 获取更多帮助

如果以上解决方案都无法解决问题，请：

1. **运行诊断脚本**并保存输出：
   ```bash
   ./scripts/diagnose-connection.sh > diagnosis.log 2>&1
   ./scripts/test-third-party-integration.sh > integration_test.log 2>&1
   ```

2. **收集系统信息**：
   ```bash
   # 系统信息
   uname -a > system_info.log
   
   # 网络状态
   netstat -an | grep LISTEN > network_status.log
   
   # 进程状态
   ps aux | grep -E "(next|langfuse|node)" > process_status.log
   
   # Docker 状态（如适用）
   docker ps -a > docker_status.log
   ```

3. **联系支持**：
   - GitHub Issues: https://github.com/langfuse/langfuse/issues
   - Discord 社区: https://discord.gg/7NXusRtqYU
   - 官方文档: https://langfuse.com/docs

## 📚 相关文档

- [第三方集成连接错误排查指南](./third-party-integration-connection-errors.md)
- [Langfuse API 文档](https://langfuse.com/docs/api)
- [部署指南](../deployment/)
- [网络配置指南](../networking/)

---

**提示**: 大多数连接被拒绝的错误都是由于 Langfuse 服务未运行或网络配置问题导致的。按照本指南的步骤进行排查，通常可以快速解决问题。
