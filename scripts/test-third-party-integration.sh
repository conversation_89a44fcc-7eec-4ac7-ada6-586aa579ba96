#!/bin/bash

# Langfuse 第三方系统集成测试脚本
# 专门用于测试第三方系统（如 RAGFlow、Dify 等）与 Langfuse 的连接

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_BASE_URL="http://localhost:3000"
DEFAULT_TIMEOUT=10

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -u, --url URL        Langfuse 基础 URL (默认: $DEFAULT_BASE_URL)"
    echo "  -t, --timeout SEC    连接超时时间 (默认: $DEFAULT_TIMEOUT 秒)"
    echo "  -k, --api-key KEY    API 密钥 (可选，用于认证测试)"
    echo "  -p, --project-id ID  项目 ID (可选，用于无认证测试)"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认设置测试本地 Langfuse"
    echo "  $0 -u https://your-domain.com        # 测试远程 Langfuse 实例"
    echo "  $0 -k sk-lf-your-api-key             # 使用 API 密钥测试"
    echo "  $0 -p your-project-id                # 使用项目 ID 测试"
}

# 测试基础连接
test_basic_connection() {
    local base_url=$1
    local timeout=$2
    
    log_info "测试基础连接: $base_url"
    
    # 测试根路径
    if curl -s --connect-timeout $timeout --max-time $timeout "$base_url" > /dev/null 2>&1; then
        log_success "根路径连接成功"
    else
        log_error "根路径连接失败"
        return 1
    fi
    
    return 0
}

# 测试健康检查端点
test_health_endpoints() {
    local base_url=$1
    local timeout=$2
    
    log_info "测试健康检查端点..."
    
    # 测试健康检查
    local health_response=$(curl -s --connect-timeout $timeout --max-time $timeout "$base_url/api/public/health" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$health_response" | grep -q '"status":"OK"'; then
        local version=$(echo "$health_response" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
        log_success "健康检查通过 (版本: $version)"
    else
        log_error "健康检查失败"
        return 1
    fi
    
    # 测试就绪检查
    local ready_response=$(curl -s --connect-timeout $timeout --max-time $timeout "$base_url/api/public/ready" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$ready_response" | grep -q '"status":"OK"'; then
        log_success "就绪检查通过"
    else
        log_error "就绪检查失败"
        return 1
    fi
    
    return 0
}

# 测试提示词 API（无认证）
test_prompts_api_no_auth() {
    local base_url=$1
    local timeout=$2
    local project_id=$3
    
    if [ -z "$project_id" ]; then
        log_warning "跳过无认证提示词 API 测试（未提供项目 ID）"
        return 0
    fi
    
    log_info "测试提示词 API（无认证模式）..."
    
    # 测试提示词列表
    local prompts_url="$base_url/api/public/v2/prompts?projectId=$project_id&limit=1"
    local prompts_response=$(curl -s --connect-timeout $timeout --max-time $timeout "$prompts_url" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$prompts_response" | grep -q '"data"'; then
        log_success "提示词列表 API 测试通过"
    else
        log_error "提示词列表 API 测试失败"
        echo "响应: $prompts_response"
        return 1
    fi
    
    # 测试智能推荐 API
    local recommend_url="$base_url/api/public/v2/prompts/recommend?projectId=$project_id&query=test&limit=1"
    local recommend_response=$(curl -s --connect-timeout $timeout --max-time $timeout "$recommend_url" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$recommend_response" | grep -q '"data"'; then
        log_success "智能推荐 API 测试通过"
    else
        log_warning "智能推荐 API 测试失败（可能是正常的，如果没有提示词数据）"
    fi
    
    return 0
}

# 测试提示词 API（认证模式）
test_prompts_api_with_auth() {
    local base_url=$1
    local timeout=$2
    local api_key=$3
    
    if [ -z "$api_key" ]; then
        log_warning "跳过认证提示词 API 测试（未提供 API 密钥）"
        return 0
    fi
    
    log_info "测试提示词 API（认证模式）..."
    
    # 测试提示词列表
    local prompts_response=$(curl -s --connect-timeout $timeout --max-time $timeout \
        -H "Authorization: Bearer $api_key" \
        "$base_url/api/public/v2/prompts?limit=1" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$prompts_response" | grep -q '"data"'; then
        log_success "认证提示词列表 API 测试通过"
    else
        log_error "认证提示词列表 API 测试失败"
        echo "响应: $prompts_response"
        return 1
    fi
    
    return 0
}

# 测试跟踪数据接口
test_ingestion_api() {
    local base_url=$1
    local timeout=$2
    local api_key=$3
    
    if [ -z "$api_key" ]; then
        log_warning "跳过跟踪数据接口测试（需要 API 密钥）"
        return 0
    fi
    
    log_info "测试跟踪数据接口..."
    
    # 创建测试跟踪数据
    local test_trace='{
        "id": "test-trace-'$(date +%s)'",
        "name": "Integration Test Trace",
        "userId": "test-user",
        "sessionId": "test-session",
        "metadata": {
            "source": "integration-test"
        }
    }'
    
    local ingestion_response=$(curl -s --connect-timeout $timeout --max-time $timeout \
        -X POST \
        -H "Authorization: Bearer $api_key" \
        -H "Content-Type: application/json" \
        -d "$test_trace" \
        "$base_url/api/public/ingestion" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "跟踪数据接口测试通过"
    else
        log_error "跟踪数据接口测试失败"
        echo "响应: $ingestion_response"
        return 1
    fi
    
    return 0
}

# 生成集成示例代码
generate_integration_examples() {
    local base_url=$1
    local api_key=$2
    local project_id=$3
    
    log_info "生成集成示例代码..."
    
    local examples_file="langfuse_integration_examples_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$examples_file" << EOF
# Langfuse 第三方系统集成示例

## 基础配置

- **Langfuse URL**: $base_url
- **API Key**: ${api_key:+已配置}${api_key:-未配置}
- **Project ID**: ${project_id:-未配置}

## JavaScript/TypeScript 集成示例

### 使用 API 密钥认证

\`\`\`javascript
const LANGFUSE_CONFIG = {
  baseUrl: '$base_url',
  apiKey: '${api_key:-your-api-key}',
  timeout: 10000
};

// 测试连接
async function testConnection() {
  try {
    const response = await fetch(\`\${LANGFUSE_CONFIG.baseUrl}/api/public/health\`);
    const data = await response.json();
    console.log('Langfuse 连接成功:', data);
  } catch (error) {
    console.error('连接失败:', error);
  }
}

// 获取提示词列表
async function getPrompts() {
  try {
    const response = await fetch(\`\${LANGFUSE_CONFIG.baseUrl}/api/public/v2/prompts?limit=10\`, {
      headers: {
        'Authorization': \`Bearer \${LANGFUSE_CONFIG.apiKey}\`
      }
    });
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('获取提示词失败:', error);
  }
}
\`\`\`

### 使用项目 ID（无认证模式）

\`\`\`javascript
const LANGFUSE_CONFIG = {
  baseUrl: '$base_url',
  projectId: '${project_id:-your-project-id}',
  timeout: 10000
};

// 搜索提示词
async function searchPrompts(query) {
  try {
    const params = new URLSearchParams({
      projectId: LANGFUSE_CONFIG.projectId,
      search: query,
      limit: 10
    });
    
    const response = await fetch(\`\${LANGFUSE_CONFIG.baseUrl}/api/public/v2/prompts?\${params}\`);
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('搜索提示词失败:', error);
  }
}
\`\`\`

## Python 集成示例

\`\`\`python
import requests
import json

class LangfuseClient:
    def __init__(self, base_url='$base_url', api_key='${api_key:-your-api-key}'):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def test_connection(self):
        try:
            response = requests.get(f'{self.base_url}/api/public/health', timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f'连接测试失败: {e}')
            return None
    
    def get_prompts(self, limit=10):
        try:
            response = requests.get(
                f'{self.base_url}/api/public/v2/prompts',
                headers=self.headers,
                params={'limit': limit},
                timeout=10
            )
            response.raise_for_status()
            return response.json()['data']
        except Exception as e:
            print(f'获取提示词失败: {e}')
            return None

# 使用示例
client = LangfuseClient()
health = client.test_connection()
if health:
    print(f"连接成功，版本: {health.get('version')}")
    prompts = client.get_prompts()
    print(f"找到 {len(prompts)} 个提示词")
\`\`\`

## cURL 测试命令

\`\`\`bash
# 健康检查
curl -X GET "$base_url/api/public/health"

# 获取提示词列表（认证模式）
curl -X GET "$base_url/api/public/v2/prompts?limit=5" \\
  -H "Authorization: Bearer ${api_key:-your-api-key}"

# 搜索提示词（无认证模式）
curl -X GET "$base_url/api/public/v2/prompts?projectId=${project_id:-your-project-id}&search=test&limit=5"
\`\`\`

## 常见错误处理

1. **Connection refused**: 检查 Langfuse 服务是否运行
2. **401 Unauthorized**: 检查 API 密钥是否正确
3. **404 Not Found**: 检查 API 端点路径是否正确
4. **Timeout**: 增加超时时间或检查网络连接

EOF

    log_success "集成示例已保存到: $examples_file"
}

# 主函数
main() {
    local base_url="$DEFAULT_BASE_URL"
    local timeout="$DEFAULT_TIMEOUT"
    local api_key=""
    local project_id=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--url)
                base_url="$2"
                shift 2
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -k|--api-key)
                api_key="$2"
                shift 2
                ;;
            -p|--project-id)
                project_id="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 移除 URL 末尾的斜杠
    base_url="${base_url%/}"
    
    echo "========================================"
    echo "  Langfuse 第三方系统集成测试工具"
    echo "========================================"
    echo ""
    echo "测试配置:"
    echo "- URL: $base_url"
    echo "- 超时: ${timeout}s"
    echo "- API Key: ${api_key:+已配置}${api_key:-未配置}"
    echo "- Project ID: ${project_id:-未配置}"
    echo ""
    
    local all_tests_passed=true
    
    # 执行测试
    test_basic_connection "$base_url" "$timeout" || all_tests_passed=false
    echo ""
    
    test_health_endpoints "$base_url" "$timeout" || all_tests_passed=false
    echo ""
    
    test_prompts_api_no_auth "$base_url" "$timeout" "$project_id" || all_tests_passed=false
    echo ""
    
    test_prompts_api_with_auth "$base_url" "$timeout" "$api_key" || all_tests_passed=false
    echo ""
    
    test_ingestion_api "$base_url" "$timeout" "$api_key" || all_tests_passed=false
    echo ""
    
    # 生成集成示例
    generate_integration_examples "$base_url" "$api_key" "$project_id"
    echo ""
    
    # 总结
    if [ "$all_tests_passed" = true ]; then
        log_success "所有测试通过！Langfuse 可以正常集成到第三方系统中。"
    else
        log_warning "部分测试失败，请检查上述错误信息。"
    fi
    
    echo ""
    echo "下一步:"
    echo "1. 查看生成的集成示例文件"
    echo "2. 根据你的第三方系统选择合适的集成方式"
    echo "3. 如遇问题，请查看故障排除文档: docs/troubleshooting/third-party-integration-connection-errors.md"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
