# Langfuse 诊断和测试脚本

本目录包含用于诊断和测试 Langfuse 连接问题的实用脚本，特别是针对第三方系统集成时遇到的 `ConnectError('[Errno 111] Connection refused')` 错误。

## 📋 脚本列表

### 1. `diagnose-connection.sh` - 全面系统诊断

**用途**: 全面诊断 Langfuse 系统的连接问题，包括服务状态、网络连接、Docker 容器等。

**使用方法**:
```bash
# 基础诊断
./scripts/diagnose-connection.sh

# 指定自定义 URL
./scripts/diagnose-connection.sh http://your-domain.com:3000
```

**功能特性**:
- ✅ 检查网络连通性
- ✅ 检查端口监听状态
- ✅ 检查 Docker 服务状态
- ✅ 测试 Langfuse API 健康状态
- ✅ 检查防火墙设置
- ✅ 生成详细诊断报告
- ✅ 提供修复建议

### 2. `test-third-party-integration.sh` - 第三方集成测试

**用途**: 专门测试第三方系统与 Langfuse 的集成连接，模拟真实的集成场景。

**使用方法**:
```bash
# 基础测试
./scripts/test-third-party-integration.sh

# 使用 API 密钥测试
./scripts/test-third-party-integration.sh -k sk-lf-your-api-key

# 使用项目 ID 测试（无认证模式）
./scripts/test-third-party-integration.sh -p your-project-id

# 测试远程实例
./scripts/test-third-party-integration.sh -u https://your-domain.com

# 完整参数示例
./scripts/test-third-party-integration.sh \
  -u http://localhost:3000 \
  -k sk-lf-your-api-key \
  -p your-project-id \
  -t 15
```

**参数说明**:
- `-u, --url URL`: Langfuse 基础 URL（默认: http://localhost:3000）
- `-k, --api-key KEY`: API 密钥（可选，用于认证测试）
- `-p, --project-id ID`: 项目 ID（可选，用于无认证测试）
- `-t, --timeout SEC`: 连接超时时间（默认: 10 秒）
- `-h, --help`: 显示帮助信息

**功能特性**:
- ✅ 测试基础连接
- ✅ 测试健康检查端点
- ✅ 测试提示词 API（认证和无认证模式）
- ✅ 测试跟踪数据接口
- ✅ 生成集成示例代码
- ✅ 提供详细的错误诊断

## 🚀 快速开始

### 场景 1: 第三方系统无法连接 Langfuse

```bash
# 1. 运行全面诊断
./scripts/diagnose-connection.sh

# 2. 运行集成测试
./scripts/test-third-party-integration.sh

# 3. 查看生成的报告和示例
ls -la *diagnosis*.log *integration_examples*.md
```

### 场景 2: 测试特定配置

```bash
# 测试生产环境配置
./scripts/test-third-party-integration.sh \
  -u https://your-langfuse-domain.com \
  -k your-production-api-key

# 测试开发环境配置
./scripts/test-third-party-integration.sh \
  -u http://localhost:3000 \
  -p your-dev-project-id
```

### 场景 3: 自动化测试集成

```bash
#!/bin/bash
# 集成测试脚本示例

echo "开始 Langfuse 集成测试..."

# 运行诊断
if ./scripts/diagnose-connection.sh; then
    echo "系统诊断通过"
else
    echo "系统诊断失败，请检查 Langfuse 服务状态"
    exit 1
fi

# 运行集成测试
if ./scripts/test-third-party-integration.sh -k "$LANGFUSE_API_KEY"; then
    echo "集成测试通过"
else
    echo "集成测试失败，请检查配置"
    exit 1
fi

echo "所有测试通过！"
```

## 📊 输出示例

### 诊断脚本输出

```
========================================
  Langfuse 第三方集成连接诊断工具
========================================

[INFO] 开始诊断 Langfuse 连接问题...

[SUCCESS] 本地回环网络正常
[SUCCESS] localhost DNS 解析正常

[SUCCESS] Langfuse Web 端口 3000 正在监听
[SUCCESS] PostgreSQL 端口 5432 正在监听
[SUCCESS] Redis 端口 6379 正在监听

[SUCCESS] Langfuse 健康检查通过 (版本: 3.103.0)
[SUCCESS] Langfuse 就绪检查通过

[SUCCESS] 诊断报告已保存到: langfuse_diagnosis_20250922_160153.log

[SUCCESS] 所有检查通过！Langfuse 服务运行正常。
```

### 集成测试脚本输出

```
========================================
  Langfuse 第三方系统集成测试工具
========================================

测试配置:
- URL: http://localhost:3000
- 超时: 10s
- API Key: 已配置
- Project ID: 已配置

[SUCCESS] 根路径连接成功
[SUCCESS] 健康检查通过 (版本: 3.103.0)
[SUCCESS] 就绪检查通过
[SUCCESS] 提示词列表 API 测试通过
[SUCCESS] 认证提示词列表 API 测试通过
[SUCCESS] 跟踪数据接口测试通过
[SUCCESS] 集成示例已保存到: langfuse_integration_examples_20250922_160420.md

[SUCCESS] 所有测试通过！Langfuse 可以正常集成到第三方系统中。
```

## 🔧 故障排除

### 常见问题

1. **脚本权限错误**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **命令未找到**
   ```bash
   # 确保安装了必要的工具
   # macOS
   brew install curl netstat

   # Ubuntu/Debian
   sudo apt-get install curl net-tools

   # CentOS/RHEL
   sudo yum install curl net-tools
   ```

3. **Docker 相关错误**
   ```bash
   # 确保 Docker 正在运行
   docker --version
   docker-compose --version
   ```

### 脚本调试

```bash
# 启用调试模式
bash -x ./scripts/diagnose-connection.sh

# 查看详细输出
./scripts/test-third-party-integration.sh -t 30 2>&1 | tee test_output.log
```

## 📁 生成的文件

脚本运行后会生成以下文件：

- `langfuse_diagnosis_YYYYMMDD_HHMMSS.log` - 系统诊断报告
- `langfuse_integration_examples_YYYYMMDD_HHMMSS.md` - 集成示例代码

这些文件包含详细的诊断信息和集成示例，可以用于：
- 问题排查和分析
- 第三方系统集成开发
- 技术支持和问题报告

## 🆘 获取帮助

如果脚本无法解决你的问题，请：

1. **查看相关文档**:
   - [连接错误解决指南](../docs/troubleshooting/connection-refused-error-guide.md)
   - [第三方集成故障排除](../docs/troubleshooting/third-party-integration-connection-errors.md)

2. **联系支持**:
   - GitHub Issues: https://github.com/langfuse/langfuse/issues
   - Discord 社区: https://discord.gg/7NXusRtqYU

3. **提供诊断信息**:
   - 运行诊断脚本并保存输出
   - 提供系统环境信息
   - 描述具体的错误场景

---

**注意**: 这些脚本设计为安全的只读诊断工具，不会修改你的系统配置或数据。
