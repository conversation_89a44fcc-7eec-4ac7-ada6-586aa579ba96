#!/bin/bash

# Langfuse 第三方集成连接诊断脚本
# 用于快速诊断和解决连接问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2

    # 尝试多种方法检查端口
    local port_listening=false

    # 方法1: netstat (Linux/Unix)
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        port_listening=true
    # 方法2: netstat (macOS)
    elif netstat -an 2>/dev/null | grep -q "LISTEN.*:$port"; then
        port_listening=true
    # 方法3: lsof
    elif command -v lsof &> /dev/null && lsof -i :$port 2>/dev/null | grep -q "LISTEN"; then
        port_listening=true
    # 方法4: 直接测试连接
    elif curl -s --connect-timeout 1 --max-time 2 "http://localhost:$port" > /dev/null 2>&1; then
        port_listening=true
    fi

    if [ "$port_listening" = true ]; then
        log_success "$service_name 端口 $port 正在监听"
        return 0
    else
        log_warning "$service_name 端口 $port 可能未在监听（或检测方法不适用）"
        return 1
    fi
}

# 检查 HTTP 服务
check_http_service() {
    local url=$1
    local service_name=$2
    
    log_info "检查 $service_name: $url"
    
    if curl -s --connect-timeout 5 --max-time 10 "$url" > /dev/null 2>&1; then
        log_success "$service_name 连接正常"
        return 0
    else
        log_error "$service_name 连接失败"
        return 1
    fi
}

# 检查 Langfuse API
check_langfuse_api() {
    local base_url=${1:-"http://localhost:3000"}
    
    log_info "检查 Langfuse API: $base_url"
    
    # 检查健康状态
    local health_response=$(curl -s --connect-timeout 5 --max-time 10 "$base_url/api/public/health" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$health_response" | grep -q '"status":"OK"'; then
        local version=$(echo "$health_response" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
        log_success "Langfuse 健康检查通过 (版本: $version)"
    else
        log_error "Langfuse 健康检查失败"
        return 1
    fi
    
    # 检查就绪状态
    local ready_response=$(curl -s --connect-timeout 5 --max-time 10 "$base_url/api/public/ready" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$ready_response" | grep -q '"status":"OK"'; then
        log_success "Langfuse 就绪检查通过"
    else
        log_error "Langfuse 就绪检查失败"
        return 1
    fi
    
    return 0
}

# 检查 Docker 服务
check_docker_services() {
    if ! check_command "docker"; then
        log_warning "Docker 未安装，跳过 Docker 服务检查"
        return 0
    fi
    
    log_info "检查 Docker 服务状态..."
    
    local services=("langfuse-web" "langfuse-postgres" "langfuse-redis" "langfuse-clickhouse" "langfuse-minio")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        local container_status=$(docker ps --filter "name=$service" --format "{{.Status}}" 2>/dev/null)
        if [ -n "$container_status" ]; then
            if echo "$container_status" | grep -q "Up"; then
                log_success "$service 容器运行正常"
            else
                log_error "$service 容器状态异常: $container_status"
                all_healthy=false
            fi
        else
            log_warning "$service 容器未找到"
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        return 0
    else
        return 1
    fi
}

# 检查网络连通性
check_network_connectivity() {
    log_info "检查网络连通性..."
    
    # 检查本地回环
    if ping -c 1 127.0.0.1 > /dev/null 2>&1; then
        log_success "本地回环网络正常"
    else
        log_error "本地回环网络异常"
        return 1
    fi
    
    # 检查 DNS 解析
    if ping -c 1 localhost > /dev/null 2>&1; then
        log_success "localhost DNS 解析正常"
    else
        log_error "localhost DNS 解析异常"
        return 1
    fi
    
    return 0
}

# 检查防火墙设置
check_firewall() {
    log_info "检查防火墙设置..."
    
    # 检查 ufw (Ubuntu/Debian)
    if command -v ufw &> /dev/null; then
        local ufw_status=$(sudo ufw status 2>/dev/null | head -1)
        if echo "$ufw_status" | grep -q "Status: active"; then
            log_warning "UFW 防火墙已启用，请确保允许端口 3000"
            sudo ufw status | grep -E "(3000|ALLOW)" || log_warning "端口 3000 可能被防火墙阻止"
        else
            log_success "UFW 防火墙未启用"
        fi
    fi
    
    # 检查 firewalld (CentOS/RHEL)
    if command -v firewall-cmd &> /dev/null; then
        if systemctl is-active --quiet firewalld; then
            log_warning "firewalld 防火墙已启用，请确保允许端口 3000"
            sudo firewall-cmd --list-ports | grep -q "3000" || log_warning "端口 3000 可能被防火墙阻止"
        else
            log_success "firewalld 防火墙未启用"
        fi
    fi
}

# 生成诊断报告
generate_report() {
    local report_file="langfuse_diagnosis_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "生成诊断报告: $report_file"
    
    {
        echo "Langfuse 连接诊断报告"
        echo "生成时间: $(date)"
        echo "系统信息: $(uname -a)"
        echo ""
        
        echo "=== 端口监听状态 ==="
        netstat -tlnp 2>/dev/null | grep -E "(3000|5432|6379|8123|9000|9090)" || echo "未找到相关端口"
        echo ""
        
        echo "=== Docker 容器状态 ==="
        docker ps -a 2>/dev/null | grep -E "(langfuse|postgres|redis|clickhouse|minio)" || echo "未找到相关容器"
        echo ""
        
        echo "=== Langfuse 健康检查 ==="
        curl -v http://localhost:3000/api/public/health 2>&1 || echo "健康检查失败"
        echo ""
        
        echo "=== 环境变量 ==="
        env | grep -E "(LANGFUSE|DATABASE|REDIS|CLICKHOUSE)" || echo "未找到相关环境变量"
        echo ""
        
        echo "=== 系统资源 ==="
        free -h 2>/dev/null || echo "内存信息获取失败"
        df -h . 2>/dev/null || echo "磁盘信息获取失败"
        
    } > "$report_file"
    
    log_success "诊断报告已保存到: $report_file"
}

# 提供修复建议
provide_suggestions() {
    log_info "修复建议:"
    echo ""
    echo "1. 如果 Langfuse 服务未运行:"
    echo "   pnpm run dev:web"
    echo "   # 或"
    echo "   docker-compose up -d langfuse-web"
    echo ""
    echo "2. 如果端口被占用:"
    echo "   lsof -i :3000"
    echo "   # 停止占用端口的进程或修改配置"
    echo ""
    echo "3. 如果防火墙阻止连接:"
    echo "   sudo ufw allow 3000  # Ubuntu/Debian"
    echo "   sudo firewall-cmd --permanent --add-port=3000/tcp  # CentOS/RHEL"
    echo ""
    echo "4. 如果 Docker 服务异常:"
    echo "   docker-compose restart"
    echo "   docker-compose logs langfuse-web"
    echo ""
    echo "5. 检查配置文件:"
    echo "   cat .env | grep -E '(NEXTAUTH_URL|DATABASE_URL)'"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  Langfuse 第三方集成连接诊断工具"
    echo "========================================"
    echo ""
    
    local base_url=${1:-"http://localhost:3000"}
    local all_checks_passed=true
    
    # 基础检查
    log_info "开始诊断 Langfuse 连接问题..."
    echo ""
    
    # 检查必要命令
    check_command "curl" || all_checks_passed=false
    check_command "netstat" || all_checks_passed=false
    
    # 检查网络连通性
    check_network_connectivity || all_checks_passed=false
    echo ""
    
    # 检查端口监听
    check_port 3000 "Langfuse Web" || all_checks_passed=false
    check_port 5432 "PostgreSQL" || all_checks_passed=false
    check_port 6379 "Redis" || all_checks_passed=false
    check_port 8123 "ClickHouse HTTP" || all_checks_passed=false
    echo ""
    
    # 检查 Docker 服务
    check_docker_services || all_checks_passed=false
    echo ""
    
    # 检查 Langfuse API
    check_langfuse_api "$base_url" || all_checks_passed=false
    echo ""
    
    # 检查防火墙
    check_firewall
    echo ""
    
    # 生成报告
    generate_report
    echo ""
    
    # 总结
    if [ "$all_checks_passed" = true ]; then
        log_success "所有检查通过！Langfuse 服务运行正常。"
    else
        log_error "发现问题，请查看上述错误信息。"
        provide_suggestions
    fi
    
    echo ""
    echo "如需更多帮助，请查看:"
    echo "- 故障排除文档: docs/troubleshooting/third-party-integration-connection-errors.md"
    echo "- GitHub Issues: https://github.com/langfuse/langfuse/issues"
    echo "- Discord 社区: https://discord.gg/7NXusRtqYU"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
