Langfuse 连接诊断报告
生成时间: 2025年 9月22日 星期一 16时01分53秒 CST
系统信息: <PERSON>-Pro-3.local 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:28:30 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T6030 arm64

=== 端口监听状态 ===
未找到相关端口

=== Docker 容器状态 ===
7a95070b0881   minio/minio                        "sh -c 'mkdir -p /da…"   4 days ago    Up 4 days (healthy)        0.0.0.0:9090->9000/tcp, [::]:9090->9000/tcp, 127.0.0.1:9091->9001/tcp   langfuse-minio-1
fa14aa9d3909   redis:7                            "docker-entrypoint.s…"   4 days ago    Up 4 days (healthy)        127.0.0.1:6379->6379/tcp                                                langfuse-redis-1
9bdd97a8f53b   postgres:latest                    "docker-entrypoint.s…"   4 days ago    Up 4 days (healthy)        127.0.0.1:5432->5432/tcp                                                langfuse-postgres-1
8bf0beaf06a3   clickhouse/clickhouse-server       "/entrypoint.sh"          4 days ago    Up 4 days (healthy)        127.0.0.1:8123->8123/tcp, 127.0.0.1:9000->9000/tcp, 9009/tcp            langfuse-clickhouse-1
33be797aafb5   redis:7-alpine                     "docker-entrypoint.s…"   2 weeks ago   Exited (0) 4 days ago                                                                              pdf2zh-redis

=== Langfuse 健康检查 ===
* Host localhost:3000 was resolved.
* IPv6: ::1
* IPv4: 127.0.0.1
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0*   Trying [::1]:3000...
* connect to ::1 port 3000 from ::1 port 58246 failed: Connection refused
*   Trying 127.0.0.1:3000...
* Connected to localhost (127.0.0.1) port 3000
> GET /api/public/health HTTP/1.1
> Host: localhost:3000
> User-Agent: curl/8.7.1
> Accept: */*
> 
* Request completely sent off
< HTTP/1.1 200 OK
< X-Robots-Tag: noindex
< X-Content-Type-Options: nosniff
< Referrer-Policy: strict-origin-when-cross-origin
< Document-Policy: js-profiling
< Permissions-Policy: autoplay=*, fullscreen=*, microphone=*
< x-frame-options: SAMEORIGIN
< Vary: Origin, Accept-Encoding
< Content-Type: application/json; charset=utf-8
< ETag: "177355bvyhlz"
< Content-Length: 35
< Date: Mon, 22 Sep 2025 08:01:53 GMT
< Connection: keep-alive
< Keep-Alive: timeout=5
< 
{ [35 bytes data]

100    35  100    35    0     0   3718      0 --:--:-- --:--:-- --:--:--  3888
* Connection #0 to host localhost left intact
{"status":"OK","version":"3.103.0"}
=== 环境变量 ===
未找到相关环境变量

=== 系统资源 ===
内存信息获取失败
Filesystem      Size    Used   Avail Capacity iused ifree %iused  Mounted on
/dev/disk3s5   460Gi   431Gi   7.7Gi    99%    4.4M   81M    5%   /System/Volumes/Data
